import { PowerHourEntry } from "@/models/power-hour";

/**
 * Utility functions to handle start and end time properties consistently
 * across the app, supporting both legacy (idealStartTime/idealEndTime) 
 * and correct (start_time/end_time) property names.
 */

/**
 * Gets the start time for an entry, preferring start_time over idealStartTime
 * @param entry PowerHourEntry object
 * @param defaultValue Default value if no start time is found
 * @returns Start time in seconds
 */
export function getEntryStartTime(entry: PowerHourEntry, defaultValue: number = 0): number {
  // Prefer the correct property name first
  if (typeof entry.start_time === 'number') {
    return entry.start_time;
  }
  
  // Fall back to legacy property name
  if (typeof entry.idealStartTime === 'number') {
    return entry.idealStartTime;
  }
  
  return defaultValue;
}

/**
 * Gets the end time for an entry, preferring end_time over idealEndTime
 * @param entry PowerHourEntry object
 * @param defaultValue Default value if no end time is found
 * @returns End time in seconds
 */
export function getEntryEndTime(entry: PowerHourEntry, defaultValue: number = 60): number {
  // Prefer the correct property name first
  if (typeof entry.end_time === 'number') {
    return entry.end_time;
  }
  
  // Fall back to legacy property name
  if (typeof entry.idealEndTime === 'number') {
    return entry.idealEndTime;
  }
  
  return defaultValue;
}

/**
 * Gets both start and end times for an entry
 * @param entry PowerHourEntry object
 * @param defaultStart Default start time if not found
 * @param defaultEnd Default end time if not found
 * @returns Object with startTime and endTime properties
 */
export function getEntryTimes(
  entry: PowerHourEntry, 
  defaultStart: number = 0, 
  defaultEnd: number = 60
): { startTime: number; endTime: number } {
  return {
    startTime: getEntryStartTime(entry, defaultStart),
    endTime: getEntryEndTime(entry, defaultEnd)
  };
}

/**
 * Formats time in seconds to MM:SS format
 * @param seconds Time in seconds
 * @returns Formatted time string
 */
export function formatTime(seconds: number): string {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
}

/**
 * Formats a time range for display
 * @param startTime Start time in seconds
 * @param endTime End time in seconds
 * @returns Formatted time range string
 */
export function formatTimeRange(startTime: number, endTime: number): string {
  return `${formatTime(startTime)} - ${formatTime(endTime)}`;
}

/**
 * Gets the duration of an entry in seconds
 * @param entry PowerHourEntry object
 * @returns Duration in seconds
 */
export function getEntryDuration(entry: PowerHourEntry): number {
  const { startTime, endTime } = getEntryTimes(entry);
  return Math.max(0, endTime - startTime);
}

/**
 * Checks if an entry has valid timing data
 * @param entry PowerHourEntry object
 * @returns True if the entry has valid start and end times
 */
export function hasValidTiming(entry: PowerHourEntry): boolean {
  const { startTime, endTime } = getEntryTimes(entry);
  return startTime >= 0 && endTime > startTime;
}
