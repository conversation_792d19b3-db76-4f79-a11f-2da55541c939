"use client";

import { useSearch<PERSON>ara<PERSON>, useRouter } from "next/navigation";
import React, { useState, useEffect } from "react";

import Loading from "@/app/power-hour-ai/loading";
import { toast } from "react-toastify";
import {
  PowerHour,
  PowerHourGenerationSteps,
} from "@/models/power-hour";
import { N8nProvider } from "@/app/providers/n8n-provider";
import { useFirestorePowerHour } from "@/hooks/use-firestore-power-hour";
import { doc, updateDoc } from "firebase/firestore";
import { firestore, COLLECTIONS } from "@/app/firebase/firebase";
import { validatePowerHour, getValidationSummary } from "@/utils/power-hour-validation";

// These modules may have TypeScript import errors during development
// but will work during runtime
// @ts-ignore
import GenerationStatus from "./generation-status";



export default function CreatePowerHour() {
  // Define all state hooks at the top level, never conditionally
  const [powerHourId, setPowerHourId] = useState("");
  const [autoRedirect, setAutoRedirect] = useState(false);
  const [generationError, setGenerationError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [initialRedirectChecked, setInitialRedirectChecked] = useState(false);

  // This hook must always be called, even if powerHourId is empty
  const { powerHour } = useFirestorePowerHour(powerHourId);

  const searchParams = useSearchParams();
  const router = useRouter();

  // Check for power hour ID in URL and redirect if not present
  useEffect(() => {
    const id = searchParams.get("powerHourId");

    if (!id || id === "") {
      // No power hour ID found, redirect to start of workflow
      console.log("No power hour ID found, redirecting to start page");
      router.push("/power-hour-ai");
      return;
    }

    console.log("Power hour ID found:", id);
    setPowerHourId(id);
    setInitialRedirectChecked(true);
  }, [searchParams, router]);

  // Check for generation errors
  useEffect(() => {
    // Check for API errors from Firebase function
    if (powerHour?.error) {
      setGenerationError(powerHour.error);
      return;
    }

    // If we have a power hour but no entries, and there's been no progress in a while
    if (
      powerHour &&
      !powerHour.entries &&
      powerHour.currentStep !== PowerHourGenerationSteps.Complete &&
      powerHour.stepProgress !== undefined
    ) {
      // For demo purposes, check if we're stuck at the video search step
      if (
        powerHour.currentStep === PowerHourGenerationSteps.SongListToVideos &&
        powerHour.stepProgress < 100
      ) {
        // Check if it's been more than 30 seconds since last update
        const now = Date.now();
        const lastUpdateTime = powerHour.lastUpdateTime || 0;

        if (now - lastUpdateTime > 30000) {
          setGenerationError(
            "API error occurred during video lookup. This could be due to rate limiting or " +
              "issues with the search query. You can try again with a different search term."
          );
        }
      }
    }
  }, [powerHour]);

  // Effect to check if power hour generation is complete
  // Function to get descriptive loading message based on power hour state
  const getLoadingStatus = (powerHour: PowerHour | null) => {
    if (!powerHour) return "Initializing power hour...";

    // Check power hour state to determine message
    if (powerHour.currentStep === PowerHourGenerationSteps.None) {
      return "Creating your power hour...";
    } else if (
      powerHour.currentStep === PowerHourGenerationSteps.GenerateSongList
    ) {
      return "Finding songs that match your theme...";
    } else if (
      powerHour.currentStep === PowerHourGenerationSteps.SongListToVideos
    ) {
      return powerHour.songs && powerHour.songs.length > 0
        ? `Finding videos for ${powerHour.songs.length} songs...`
        : "Finding videos for your songs...";
    } else if (
      powerHour.currentStep === PowerHourGenerationSteps.buildPowerHour
    ) {
      return powerHour.videos && powerHour.videos.length > 0
        ? `Creating entries for ${powerHour.videos.length} videos...`
        : "Creating power hour entries...";
    } else if (powerHour.currentStep === PowerHourGenerationSteps.Complete) {
      return "Finalizing your power hour...";
    }

    return "Processing your power hour...";
  };

  // Check if power hour has all required fields
  const isPowerHourComplete = (ph: PowerHour | null) => {
    // First check that we have a power hour with an ID
    if (!ph || !ph.id) {
      return false;
    }

    // Then check the generation status
    const generationComplete = Boolean(
      ph.currentStep === PowerHourGenerationSteps.Complete &&
      ph.stepProgress === 100
    );

    if (!generationComplete) {
      return false;
    }

    // Then validate the power hour has the necessary data to play
    const validation = validatePowerHour(ph);
    return validation.canPlay;
  };



  // Process power hour updates
  // This is separated from the monitoring effect to avoid conditional hooks
  useEffect(() => {
    // Don't process updates until we have confirmed the redirect check
    if (!initialRedirectChecked) return;

    // Don't process updates until we have a power hour
    if (!powerHour) return;

    console.log("Power hour update:", {
      step: powerHour.currentStep,
      progress: powerHour.stepProgress,
      hasSongs: Boolean(powerHour.songs?.length),
      hasVideos: Boolean(powerHour.videos?.length),
      hasEntries: Boolean(powerHour.entries?.length),
      error: powerHour.error,
    });

    // Check for stages of completion
    if (powerHour.songs && powerHour.songs.length > 0) {
      // Only show toast once when songs are first loaded
      if (!autoRedirect) {
        toast.info("Songs generated successfully!");
      }
    }

    if (powerHour.videos && powerHour.videos.length > 0) {
      // Only show toast once when videos are first loaded
      if (!autoRedirect) {
        toast.info("Videos found successfully!");
      }
    }

    // Final completion check
    if (isPowerHourComplete(powerHour) && !autoRedirect) {
      console.log("Power hour completion detected:", {
        powerHourId: powerHour?.id,
        stateId: powerHourId,
        hasEntries: Boolean(powerHour?.entries?.length),
        entriesCount: powerHour?.entries?.length
      });

      // Set a flag to prevent multiple toasts/redirects
      setAutoRedirect(true);

      // Get validation summary for user feedback
      const validation = validatePowerHour(powerHour);
      const summary = getValidationSummary(validation);

      if (validation.canPlay) {
        toast.success(`Your Power Hour is ready! ${summary}`);
      } else {
        toast.warning(`Power Hour generated but has issues: ${summary}`);
      }

      // Redirect to power hour details page (same as clicking from gallery)
      // Prefer the power hour's own ID, but fall back to the state ID
      const redirectId = powerHour.id || powerHourId;

      if (!redirectId) {
        console.error("Cannot redirect: No power hour ID available", {
          powerHourObjectId: powerHour.id,
          stateId: powerHourId,
          powerHourExists: !!powerHour,
          powerHourTitle: powerHour?.title
        });
        toast.error("Error: Power hour ID is missing. Please try creating again.");
        return;
      }

      // Additional validation - make sure the IDs match if both exist
      if (powerHour.id && powerHourId && powerHour.id !== powerHourId) {
        console.warn("Power hour ID mismatch:", {
          powerHourObjectId: powerHour.id,
          stateId: powerHourId
        });
        // Use the power hour's own ID as it's more authoritative
      }

      console.log("Redirecting to power hour details with ID:", redirectId);
      router.push(`/power-hour-ai/power-hour-details?powerHourId=${redirectId}`);
    }

    // Error detection
    if (powerHour.error && !generationError) {
      console.error("Power hour error:", powerHour.error);
      setGenerationError(powerHour.error);
    }
  }, [
    powerHour,
    initialRedirectChecked,
    autoRedirect,
    generationError,
    powerHourId,
    router,
  ]);

  // Monitor power hour ID setup and logging
  useEffect(() => {
    if (!powerHourId) return;
    if (!initialRedirectChecked) return;

    console.log(
      `Monitoring updates for power hour ${powerHourId} via Firestore`
    );
    // No cleanup needed since the hooks handle subscriptions
  }, [powerHourId, initialRedirectChecked]);



  // Handle retry of generation
  const handleRetry = async () => {
    if (powerHour) {
      try {
        setRetryCount((prev) => prev + 1);
        setGenerationError(null);
        setAutoRedirect(false);

        // Ensure firestore is not null before proceeding
        if (!firestore) {
          toast.error("Firestore is not initialized. Please try again later.");
          return;
        }

        // Reset any error state on the power hour using Firestore
        const powerHourRef = doc(
          firestore,
          COLLECTIONS.POWER_HOURS,
          powerHourId
        );
        await updateDoc(powerHourRef, {
          error: "",
          currentStep: PowerHourGenerationSteps.None,
          stepProgress: 0,
          lastUpdateTime: Date.now(),
        });

        // Use N8nProvider for retrying power hour creation
        toast.info("Retrying power hour generation...");
        // Use the original song count from the power hour entries, or default to 60
        const songCount = powerHour.entries?.length || powerHour.songs?.length || 60;
        const result = await N8nProvider.createPowerHour(powerHour.title, songCount);

        // If there's an immediate error, update the UI
        if (result?.error) {
          setGenerationError(
            result.error.error || "Failed to restart generation"
          );
        } else {
          toast.info("Restarted power hour generation.");
        }
      } catch (error) {
        console.error("Error retrying power hour generation:", error);
        toast.error("Failed to retry generation. Please try again.");
      }
    }
  };

  // Define the status-related props to avoid TypeScript errors
  const statusProps = {
    currentStep: powerHour?.currentStep,
    progress: powerHour?.stepProgress,
    errorMessage: generationError || undefined,
    onRetry: handleRetry,
    loadingMessage: getLoadingStatus(powerHour),
    hasVideos: powerHour?.videos && powerHour.videos.length > 0,
    hasSongs: powerHour?.songs && powerHour.songs.length > 0,
    videoCount: powerHour?.videos?.length || 0,
    songCount: powerHour?.songs?.length || 0,
    hasEntries: powerHour?.entries && powerHour.entries.length > 0,
    entryCount: powerHour?.entries?.length || 0,
  };

  return (
    <div className="min-h-screen h-full w-full flex flex-col bg-gray-900">
      <div className="py-4 w-full flex items-center justify-center text-white border-b border-gray-800">
        <h1 className="text-xl font-semibold">
          {powerHour?.title || "Creating Power Hour..."}
        </h1>
      </div>

      {/* Main content area that should expand to fill available space */}
      <div className="flex-grow flex items-center justify-center p-4">
        {/* Show generation status when powerHour exists but entries don't yet */}
        {powerHour && !powerHour.entries ? (
          <GenerationStatus {...statusProps} />
        ) : (
          <Loading />
        )}
      </div>
    </div>
  );
}
