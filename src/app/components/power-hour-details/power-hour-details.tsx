import React, { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import Loading from "@/app/power-hour-ai/loading";
import { useFirestorePowerHour } from "@/hooks/use-firestore-power-hour";
import { <PERSON><PERSON>, <PERSON> } from "@nextui-org/react";
import FirestoreStatus from "../firestore-status";
import ClientOnly from "../utils/client-only";
import { FaPlay, FaMusic, FaClock, FaCalendarAlt, FaTags } from "react-icons/fa";
import { PowerHourEntry } from "@/models/power-hour";
import PowerHourValidationStatus from "../power-hour-validation/power-hour-validation-status";
import { getEntryTimes, formatTime } from "@/utils/entry-time-utils";

// Compact entry component for the list view
const CompactEntryItem = ({ entry, index }: { entry: PowerHourEntry; index: number }) => {
  const { startTime, endTime } = getEntryTimes(entry);

  return (
    <div className="flex items-center gap-3 p-3 bg-gray-700 rounded-lg hover:bg-gray-600 transition-colors">
      {/* Track number */}
      <div className="flex-shrink-0 w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-sm font-bold">
        {index + 1}
      </div>

      {/* Thumbnail */}
      <div className="flex-shrink-0">
        <img
          src={entry.backup_video?.thumbnail || entry.video.thumbnail}
          alt={`${entry.song.title} thumbnail`}
          className="w-12 h-12 rounded object-cover"
        />
      </div>

      {/* Song info */}
      <div className="flex-grow min-w-0">
        <div className="font-medium text-white truncate">{entry.song.title}</div>
        <div className="text-sm text-gray-300 truncate">{entry.song.artist}</div>
      </div>

      {/* Duration */}
      <div className="flex-shrink-0 text-xs text-gray-400 flex items-center gap-1">
        <FaClock />
        <span>{formatTime(startTime)}-{formatTime(endTime)}</span>
      </div>
    </div>
  );
};

const PowerHourDetails = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { powerHour, loading, error, connectionError, firestoreError } = useFirestorePowerHour();

  // Get power hour ID from query params for navigation
  const powerHourId = searchParams.get("powerHourId");

  const onStartThisPowerHour = () => {
    if (powerHourId) {
      router.push(`/power-hour-ai/active-power-hour?powerHourId=${powerHourId}`);
    }
  };

  // Helper function to format date
  const formatDate = (timestamp?: number) => {
    if (!timestamp) return null;
    const date = new Date(timestamp);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  // Show error if no power hour ID is provided
  if (!powerHourId) {
    return (
      <ClientOnly fallback={<Loading />}>
        <div className="flex flex-col h-full bg-gray-800 text-white overflow-hidden">
          <div className="w-full h-full flex items-center justify-center">
            <div className="text-center p-8">
              <h3 className="text-xl font-bold mb-2 text-red-400">No Power Hour ID Provided</h3>
              <p className="text-gray-400 mb-4">
                A power hour ID is required to view this page.
              </p>
              <Button onClick={() => router.push('/power-hour-ai')}>
                Go Back to Power Hour AI
              </Button>
            </div>
          </div>
        </div>
      </ClientOnly>
    );
  }

  return (
    <ClientOnly fallback={<Loading />}>
      <div className="flex flex-col h-full bg-gray-800 text-white overflow-hidden">
        {loading && !error ? (
          <Loading />
        ) : powerHour && powerHour.entries?.length > 0 ? (
        <>
          {/* Header Section */}
          <div className="bg-gray-900 p-4 border-b border-gray-700">
            <div className="max-w-4xl mx-auto">
              <h1 className="text-2xl font-bold text-center mb-2">{powerHour.title}</h1>

              {/* Metadata chips */}
              <div className="flex flex-wrap justify-center gap-2 mb-4">
                <Chip size="sm" variant="flat" color="primary" startContent={<FaMusic />}>
                  {powerHour.entries.length} songs
                </Chip>
                {powerHour.mostSimilarGenre && (
                  <Chip size="sm" variant="flat" color="secondary" startContent={<FaTags />}>
                    {powerHour.mostSimilarGenre}
                  </Chip>
                )}
                {powerHour.createdAt && (
                  <Chip size="sm" variant="flat" color="default" startContent={<FaCalendarAlt />}>
                    {formatDate(powerHour.createdAt)}
                  </Chip>
                )}
              </div>

              {/* Validation Status */}
              <div className="flex justify-center mb-4">
                <PowerHourValidationStatus
                  powerHour={powerHour}
                  showDetails={true}
                  className="text-center"
                />
              </div>

              {/* Description */}
              {powerHour.description && (
                <p className="text-gray-300 text-center text-sm max-w-2xl mx-auto">
                  {powerHour.description}
                </p>
              )}
            </div>
          </div>

          {/* Hero CTA Section */}
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 p-6">
            <div className="max-w-4xl mx-auto text-center">
              <h2 className="text-xl font-semibold mb-3">Ready to start your power hour?</h2>
              <Button
                onClick={onStartThisPowerHour}
                size="lg"
                color="warning"
                variant="solid"
                startContent={<FaPlay />}
                className="font-bold text-lg px-8 py-3 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
              >
                Start This Power Hour
              </Button>
              <p className="text-blue-100 text-sm mt-2">
                60 minutes • {powerHour.entries.length} songs • 1 minute each
              </p>
            </div>
          </div>

          {/* Compact Song List */}
          <div className="flex-grow overflow-auto p-4">
            <div className="max-w-4xl mx-auto">
              <h3 className="text-lg font-semibold mb-4 text-center">Track List</h3>
              <div className="space-y-2">
                {powerHour.entries.map((entry, index) => (
                  <CompactEntryItem
                    key={entry.id || `${entry.song.id}-${entry.video.id}` || `entry-${index}`}
                    entry={entry}
                    index={index}
                  />
                ))}
              </div>
            </div>
          </div>
        </>
      ) : error === null ? (
        <div className="w-full h-full flex items-center justify-center">
          <div className="text-center p-8">
            <h3 className="text-xl font-bold mb-2">No Power Hour Found</h3>
            <p className="text-gray-400 mb-4">
              The power hour you're looking for doesn't exist or hasn't been created yet.
            </p>
            <Button onClick={() => router.push('/power-hour-ai')}>
              Go Back
            </Button>
          </div>
        </div>
      ) : (
        <div className="w-full h-full flex items-center justify-center p-8">
          <div className="max-w-md w-full">
            <div className="text-center mb-6">
              <h3 className="text-xl font-bold mb-2 text-red-400">Error Loading Power Hour</h3>
              <p className="text-gray-400 mb-4">
                There was a problem loading this power hour.
              </p>
            </div>

            <FirestoreStatus
              error={error}
              connectionError={connectionError}
              firestoreError={firestoreError}
              loading={loading}
              showDetails={true}
            />

            <div className="flex gap-3 justify-center mt-6">
              <Button
                onClick={() => window.location.reload()}
                color="primary"
              >
                Retry
              </Button>
              <Button
                onClick={() => router.push('/power-hour-ai')}
                variant="bordered"
              >
                Go Back
              </Button>
            </div>
          </div>
        </div>
      )}
      </div>
    </ClientOnly>
  );
};

export default PowerHourDetails;
