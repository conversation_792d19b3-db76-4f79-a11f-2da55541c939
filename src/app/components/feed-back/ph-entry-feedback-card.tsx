import { PowerHourEntry } from "../../../models/power-hour";
import useFeedback from "../../providers/use-feedback";
import React, { useState, useEffect } from "react";
import { FaCheck, FaTimes, FaPlay, FaClock, FaMusic, FaCalendarAlt } from "react-icons/fa";
import { getEntryTimes, formatTime, formatTimeRange } from "../../../utils/entry-time-utils";

const PHEntryFeedbackCard = ({
	powerHourId,
	entry,
}: {
	powerHourId: string;
	entry: PowerHourEntry;
}) => {
	const { upVoteEntry, downVoteEntry } = useFeedback(powerHourId);

	const handleFeedback = (entryId: string, like: boolean) => {
		console.log(`Feedback for ${entryId}: ${like ? "Like" : "Dislike"}`);
	};

	// Get entry times using utility function
	const { startTime, endTime } = getEntryTimes(entry);

	return (
		<div className="relative group cursor-pointer rounded-lg overflow-hidden bg-gray-900 text-white">
			{/* Mobile-first horizontal layout */}
			<div className="flex flex-col sm:flex-row h-full">
				{/* Thumbnail section */}
				<div className="relative flex-shrink-0 w-full sm:w-32 h-32 sm:h-auto">
					{entry.video.thumbnail ? (
						<img
							src={entry.video.thumbnail}
							alt={`${entry.song.title} thumbnail`}
							className="w-full h-full object-cover"
						/>
					) : (
						<div className="w-full h-full bg-gray-700 flex items-center justify-center">
							<FaMusic className="text-gray-500 text-2xl" />
						</div>
					)}
					{/* Video provider badge */}
					<div className="absolute top-2 right-2 bg-black bg-opacity-75 px-2 py-1 rounded text-xs">
						{entry.video.provider}
					</div>
				</div>

				{/* Content section */}
				<div className="flex-1 p-4 min-h-0">
					{/* Song title and artist */}
					<div className="mb-2">
						<h3 className="font-bold text-lg leading-tight mb-1 overflow-hidden" style={{
							display: '-webkit-box',
							WebkitLineClamp: 2,
							WebkitBoxOrient: 'vertical'
						}}>
							{entry.song.title}
						</h3>
						<p className="text-gray-300 text-sm overflow-hidden whitespace-nowrap text-ellipsis">
							{entry.song.artist}
						</p>
					</div>

					{/* Metadata grid */}
					<div className="grid grid-cols-2 gap-2 text-xs text-gray-400 mb-3">
						{/* Album and Year */}
						{(entry.song.album || entry.song.year) && (
							<div className="flex items-center gap-1">
								<FaCalendarAlt className="flex-shrink-0" />
								<span className="truncate">
									{entry.song.album && entry.song.year
										? `${entry.song.album} (${entry.song.year})`
										: entry.song.album || entry.song.year
									}
								</span>
							</div>
						)}

						{/* Genre */}
						{entry.song.genre && (
							<div className="flex items-center gap-1">
								<FaMusic className="flex-shrink-0" />
								<span className="truncate">{entry.song.genre}</span>
							</div>
						)}

						{/* Duration */}
						{entry.song.durationInSeconds && (
							<div className="flex items-center gap-1">
								<FaClock className="flex-shrink-0" />
								<span>{formatTime(entry.song.durationInSeconds)}</span>
							</div>
						)}

						{/* Play time range */}
						<div className="flex items-center gap-1">
							<FaPlay className="flex-shrink-0" />
							<span>{formatTimeRange(startTime, endTime)}</span>
						</div>
					</div>

					{/* Stats if available */}
					{entry.stats && (entry.stats.upvotes || entry.stats.downvotes || entry.stats.views) && (
						<div className="flex gap-3 text-xs text-gray-400 mb-2">
							{entry.stats.upvotes && (
								<span className="flex items-center gap-1">
									<FaCheck className="text-green-500" />
									{entry.stats.upvotes}
								</span>
							)}
							{entry.stats.downvotes && (
								<span className="flex items-center gap-1">
									<FaTimes className="text-red-500" />
									{entry.stats.downvotes}
								</span>
							)}
							{entry.stats.views && (
								<span>{entry.stats.views} views</span>
							)}
						</div>
					)}

					{/* Mobile feedback buttons - always visible on small screens */}
					<div className="flex gap-2 mt-auto pt-2 sm:hidden">
						<button
							className="flex-1 bg-green-600 hover:bg-green-700 rounded-lg py-2 px-3 flex items-center justify-center gap-2 text-sm font-medium transition-colors"
							onClick={() => handleFeedback(entry.id, true)}
							aria-label={`Like ${entry.song.title}`}
						>
							<FaCheck size="14" />
							Like
						</button>
						<button
							className="flex-1 bg-red-600 hover:bg-red-700 rounded-lg py-2 px-3 flex items-center justify-center gap-2 text-sm font-medium transition-colors"
							onClick={() => handleFeedback(entry.id, false)}
							aria-label={`Dislike ${entry.song.title}`}
						>
							<FaTimes size="14" />
							Dislike
						</button>
					</div>
				</div>
			</div>

			{/* Desktop feedback overlay - visible on hover for larger screens */}
			<div className="absolute top-0 left-0 w-full h-full bg-black bg-opacity-50 items-center justify-evenly opacity-0 group-hover:opacity-100 transition-opacity duration-300 hidden sm:flex">
				<button
					className="rounded-lg hover:bg-green-900 focus:ring focus:ring-green-800 p-4 min-h-[44px] min-w-[44px] flex items-center justify-center"
					onClick={() => handleFeedback(entry.id, true)}
					aria-label={`Like ${entry.song.title}`}
				>
					<FaCheck className="text-green-500" size="24" />
				</button>
				<button
					className="rounded-lg hover:bg-red-900 focus:ring focus:ring-red-800 p-4 min-h-[44px] min-w-[44px] flex items-center justify-center"
					onClick={() => handleFeedback(entry.id, false)}
					aria-label={`Dislike ${entry.song.title}`}
				>
					<FaTimes className="text-red-500" size="24" />
				</button>
			</div>
		</div>
	);
};

export default PHEntryFeedbackCard;
