import React, { useEffect, useState, useCallback } from "react";
import { FaThumbsUp, FaThumbsDown, FaBug, Fa<PERSON><PERSON><PERSON>, FaRand<PERSON>, Fa<PERSON>lock, Fa<PERSON>rench, FaMusic } from "react-icons/fa";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ontent, Mo<PERSON><PERSON><PERSON><PERSON>, ModalBody, ModalFooter, useDisclosure } from "@nextui-org/react";
import { Popover, PopoverTrigger, PopoverContent, Tooltip } from "@nextui-org/react";
import { submitFeedbackPowerHourEntry } from "@/app/providers/feedback";
import type { PHEntryFeedback as FeedbackType } from "@/models/feedback";
import { PHEFeedbackOpts } from "@/models/feedback";
import { PowerHourID } from "@/models/power-hour";
import { toast } from "react-toastify";
import { formatTime } from "@/utils/entry-time-utils";
import { motion, AnimatePresence } from "framer-motion";

interface PHEntryFeedbackProps {
  powerHourId: PowerHourID;
  powerHourEntryId: string;
  onSkipTrack?: () => void;
  onAdjustTimeframe?: (startTime: number, endTime: number) => void;
  onFindAlternative?: () => void;
  currentStartTime?: number;
  currentEndTime?: number;
  currentSongHasIssues?: boolean;
}

const PHEntryFeedback: React.FC<PHEntryFeedbackProps> = ({
  powerHourId,
  powerHourEntryId,
  onSkipTrack,
  onAdjustTimeframe,
  onFindAlternative,
  currentStartTime = 0,
  currentEndTime = 60,
  currentSongHasIssues = false,
}) => {
  const [feedbackVote, setFeedbackVote] = useState("");
  const [feedbackData, setFeedbackData] = useState<Partial<FeedbackType>>({
    powerHourId: powerHourId,
    powerHourEntryId: powerHourEntryId,
  });

  // For time adjustment modal
  const { isOpen: isTimeModalOpen, onOpen: onTimeModalOpen, onClose: onTimeModalClose } = useDisclosure();
  const [newStartTime, setNewStartTime] = useState(currentStartTime);
  const [newEndTime, setNewEndTime] = useState(currentEndTime);

  // For issue resolution modal
  const { isOpen: isIssueModalOpen, onOpen: onIssueModalOpen, onClose: onIssueModalClose } = useDisclosure();
  const [selectedIssue, setSelectedIssue] = useState<PHEFeedbackOpts | null>(null);

  const commonIssues = [
    PHEFeedbackOpts.SongDoesntMatchTitle,
    PHEFeedbackOpts.SongDoesntMatchVideo,
    PHEFeedbackOpts.SongIsntMusic,
    PHEFeedbackOpts.VideoIsntMusicVideo,
    PHEFeedbackOpts.ProblemWithSong,
    PHEFeedbackOpts.ProblemWithVideo,
    PHEFeedbackOpts.ProblemWithPlayback,
  ];

  const toggleFeedbackVote = useCallback((upvote?: boolean, downvote?: boolean) => {
    if (upvote && feedbackVote === "up") {
      setFeedbackVote("");
    } else if (downvote && feedbackVote === "down") {
      setFeedbackVote("");
    } else {
      setFeedbackVote(upvote ? "up" : downvote ? "down" : "");
    }
  }, [feedbackVote]);

  const updateFeedback = useCallback(({ upvote, downvote, type }: { upvote?: boolean; downvote?: boolean; type?: PHEFeedbackOpts }) => {
    if (!powerHourId || !powerHourEntryId) {
      toast.error("Error submitting feedback, missing PHID or PHEID");
      return;
    }
    
    if (upvote || downvote) {
      toggleFeedbackVote(upvote, downvote);
    }

    let updatedFeedbackData: Partial<FeedbackType> = structuredClone(feedbackData);

    if (upvote) {
      updatedFeedbackData.upvotes = 1;
      delete updatedFeedbackData.downvotes;
    } else if (downvote) {
      delete updatedFeedbackData.upvotes;
      updatedFeedbackData.downvotes = 1;
    }

    if (type && updatedFeedbackData) {
      updatedFeedbackData.feedback = {
        ...updatedFeedbackData.feedback,
        [type]: updatedFeedbackData.feedback?.[type] ? 0 : 1,
      } as any;
    }
    
    setFeedbackData(updatedFeedbackData);
    
    // Submit the feedback to the server
    submitFeedbackPowerHourEntry(updatedFeedbackData);
  }, [powerHourId, powerHourEntryId, feedbackData, toggleFeedbackVote]);

  // Update slider values when the timeframe props change
  useEffect(() => {
    setNewStartTime(currentStartTime);
    setNewEndTime(currentEndTime);
  }, [currentStartTime, currentEndTime]);
  
  // Submit feedback when component unmounts or entry changes
  useEffect(() => {
    // Reset feedback state for new entry
    setFeedbackData({
      powerHourId: powerHourId,
      powerHourEntryId: powerHourEntryId,
    });
    setFeedbackVote("");
    
    // Store current feedbackData in a ref to avoid dependency cycle
    const currentFeedbackData = feedbackData;
    
    // Cleanup function to submit any pending feedback
    return () => {
      if (currentFeedbackData.powerHourId && currentFeedbackData.powerHourEntryId) {
        submitFeedbackPowerHourEntry(currentFeedbackData);
      }
    };
  }, [powerHourId, powerHourEntryId]); // Removed feedbackData from dependencies

  // Handle fixing the song timing
  const handleTimeAdjustment = useCallback(() => {
    if (onAdjustTimeframe) {
      onAdjustTimeframe(newStartTime, newEndTime);
      toast.success(`Timeframe adjusted! How does it sound now?`);
      updateFeedback({ type: PHEFeedbackOpts.ProblemWithSong }); // Record the feedback
      onTimeModalClose();
    }
  }, [newStartTime, newEndTime, onAdjustTimeframe, onTimeModalClose, updateFeedback]);
  
  // Handle an issue with feedback and solution options
  const handleIssueSubmit = useCallback((issue: PHEFeedbackOpts) => {
    setSelectedIssue(issue);
    updateFeedback({ type: issue });
    onIssueModalOpen();
  }, [onIssueModalOpen, updateFeedback]);

	return (
		<>
			<AnimatePresence>
				{currentSongHasIssues && (
					<motion.div 
						initial={{ opacity: 0, y: 20 }}
						animate={{ opacity: 1, y: 0 }}
						exit={{ opacity: 0, y: 20 }}
						style={{ 
							margin: "0 0 0.75rem 0", 
							padding: "0.5rem", 
							borderRadius: "0.5rem", 
							backgroundColor: "rgba(153, 27, 27, 0.7)", 
							textAlign: "center", 
							fontSize: "0.875rem", 
							color: "white" 
						}}
					>
						Having trouble with this song? Use the options below to fix it!
					</motion.div>
				)}
			</AnimatePresence>
			
			<div className="flex flex-col items-center justify-center w-full pt-2 text-white">
				{/* Quick fix options row */}
				<div className="flex items-center justify-center gap-2 mb-3 flex-wrap">
					{onSkipTrack && (
						<Tooltip content="Skip this song">
							<Button 
								size="sm" 
								variant="flat" 
								color="warning" 
								onClick={onSkipTrack}
								className="min-w-0"
								startContent={<FaForward className="text-xs" />}
							>
								Skip
							</Button>
						</Tooltip>
					)}
					
					{onAdjustTimeframe && (
						<Tooltip content="Adjust song timeframe">
							<Button 
								size="sm" 
								variant="flat" 
								color="primary" 
								onClick={onTimeModalOpen}
								className="min-w-0"
								startContent={<FaClock className="text-xs" />}
							>
								Fix Timing
							</Button>
						</Tooltip>
					)}
					
					{onFindAlternative && (
						<Tooltip content="Find a different version">
							<Button 
								size="sm" 
								variant="flat" 
								color="success" 
								onClick={onFindAlternative}
								className="min-w-0"
								startContent={<FaRandom className="text-xs" />}
							>
								Alternative
							</Button>
						</Tooltip>
					)}
				</div>
				
				{/* Feedback buttons */}
				<div className="flex items-center justify-center gap-3">
					<Button
						onClick={() => updateFeedback({ upvote: true })}
						color={feedbackVote === "up" ? "success" : "default"}
						endContent={<FaThumbsUp />}
						size="sm"
						variant="flat"
					>
						Good Song
					</Button>
					<Button
						onClick={() => updateFeedback({ downvote: true })}
						color={feedbackVote === "down" ? "danger" : "default"}
						endContent={<FaThumbsDown />}
						size="sm"
						variant="flat"
					>
						Bad Song
					</Button>
					<Button 
						color="default" 
						endContent={<FaBug />} 
						size="sm" 
						variant="flat"
						onClick={() => {
							setSelectedIssue(null);
							onIssueModalOpen();
						}}
					>
						Report Issue
					</Button>
				</div>
			</div>
			
			{/* Time Adjustment Modal */}
			<Modal isOpen={isTimeModalOpen} onClose={onTimeModalClose} backdrop="blur">
				<ModalContent className="bg-gray-800 text-white">
					<ModalHeader className="flex flex-col gap-1">Adjust Song Timeframe</ModalHeader>
					<ModalBody>
						<p className="text-sm mb-4">Find the right part of the song for your power hour:</p>
						
						<div className="mb-3">
							<label className="text-sm text-gray-300 mb-1 block">Start Time: {formatTime(newStartTime)}</label>
							<input 
								type="range"
								min={0}
								max={Math.max(0, Math.min(newEndTime - 30, 180))}
								value={newStartTime}
								step={1}
								className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
								onChange={(e) => setNewStartTime(Number(e.target.value))}
							/>
						</div>
						
						<div className="mb-4">
							<label className="text-sm text-gray-300 mb-1 block">End Time: {formatTime(newEndTime)}</label>
							<input 
								type="range"
								min={newStartTime + 30}
								max={300}
								value={newEndTime}
								step={1}
								className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
								onChange={(e) => setNewEndTime(Number(e.target.value))}
							/>
						</div>
						
						<p className="text-sm text-blue-300">
							Clip Length: {formatTime(newEndTime - newStartTime)}
						</p>
					</ModalBody>
					<ModalFooter>
						<Button color="danger" variant="light" onClick={onTimeModalClose}>
							Cancel
						</Button>
						<Button color="primary" onClick={handleTimeAdjustment}>
							Apply Changes
						</Button>
					</ModalFooter>
				</ModalContent>
			</Modal>
			
			{/* Issue Resolution Modal */}
			<Modal isOpen={isIssueModalOpen} onClose={onIssueModalClose} backdrop="blur" size="lg">
				<ModalContent className="bg-gray-800 text-white">
					<ModalHeader className="flex flex-col gap-1">What's the issue?</ModalHeader>
					<ModalBody>
						{selectedIssue ? (
							<>
								<div className="p-3 bg-blue-900/30 rounded-lg mb-4">
									<p className="text-sm mb-2">Issue reported: <span className="font-semibold">{selectedIssue}</span></p>
									<p className="text-xs">Thanks for your feedback! Here&apos;s what you can do:</p>
								</div>
								
								<div className="space-y-3">
									{onSkipTrack && (
										<Button 
											color="warning" 
											className="w-full justify-start"
											onClick={() => {
												onSkipTrack();
												onIssueModalClose();
											}}
											startContent={<FaForward />}
										>
											Skip to Next Song
										</Button>
									)}
									
									{onAdjustTimeframe && (
										<Button 
											color="primary" 
											className="w-full justify-start"
											onClick={() => {
												onIssueModalClose();
												onTimeModalOpen();
											}}
											startContent={<FaClock />}
										>
											Adjust Song Timing
										</Button>
									)}
									
									{onFindAlternative && (
										<Button 
											color="success" 
											className="w-full justify-start"
											onClick={() => {
												onFindAlternative();
												onIssueModalClose();
											}}
											startContent={<FaMusic />}
										>
											Find Alternative Version
										</Button>
									)}
								</div>
							</>
						) : (
							<>
								<p className="text-sm mb-4">Select what's wrong with this track:</p>
								<ul className="space-y-2">
									{commonIssues.map((issue, index) => (
										<li
											key={index}
											className={`p-2 rounded-md cursor-pointer hover:bg-gray-600 flex items-center ${
												feedbackData?.feedback && feedbackData.feedback[issue]
													? "bg-blue-900/50 text-blue-300"
													: "text-white"
											}`}
											onClick={() => handleIssueSubmit(issue)}
										>
											<span className="mr-2"><FaWrench className="text-xs" /></span>
											{issue}
										</li>
									))}
								</ul>
							</>
						)}
					</ModalBody>
					<ModalFooter>
						<Button color="default" variant="light" onClick={onIssueModalClose}>
							Close
						</Button>
					</ModalFooter>
				</ModalContent>
			</Modal>
		</>
	);
};

export default PHEntryFeedback;
