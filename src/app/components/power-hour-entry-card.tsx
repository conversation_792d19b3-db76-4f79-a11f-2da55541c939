import { PowerHourEntry } from "@/models/power-hour";
import Image from "next/image";
import React from "react";
import { FaThumbsDown, FaThumbsUp } from "react-icons/fa";
import StatsBar from "./stats-bar";
import { getEntryTimes, formatTime } from "@/utils/entry-time-utils";

interface PowerHourEntryCardProps {
	entry: PowerHourEntry;
}

const PowerHourEntryCard: React.FC<PowerHourEntryCardProps> = ({ entry }) => {
	const { startTime, endTime } = getEntryTimes(entry);

	return (
		<div className="border border-gray-200 rounded overflow-hidden shadow-lg max-w-lg max-h-96 m-4">
			{/* Use Next.js Image component for optimized images */}
			<img
				src={entry.video.thumbnail}
				alt={`Thumbnail for ${entry.song.title}`}
				className="w-full max-h-48 object-cover"
			/>
			<div className="px-6 py-4">
				<div className="font-bold text-xl mb-2">{entry.song.title}</div>
				<p className="text-gray-700 text-base">By {entry.song.artist}</p>
				<p className="text-gray-600 text-sm">
					Start: {formatTime(startTime)} | End: {formatTime(endTime)}
				</p>
			</div>

			<StatsBar
				upvotes={entry?.stats?.upvotes}
				downvotes={entry?.stats?.downvotes}
				views={entry?.stats?.views}
			/>
		</div>
	);
};

export default PowerHourEntryCard;
