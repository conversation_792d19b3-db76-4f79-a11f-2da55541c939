"use client";

import { useEffect, useState } from "react";
import {
  doc,
  collection,
  query,
  where,
  limit,
} from "firebase/firestore";
import { useDocument, useCollection } from "react-firebase-hooks/firestore";
import {
  firestore,
  isFirestoreAvailable,
  onFirebaseReady,
  COLLECTIONS,
} from "../app/firebase/firebase";
import {
  PowerHour,
  PowerHourID,
  PowerHourGenerationSteps,
} from "../models/power-hour";

/**
 * Hook to fetch and watch a power hour document from Firestore
 * @param powerHourId The ID of the power hour to fetch
 * @returns Object containing the power hour data, loading state, and error
 */
export const useFirestorePowerHour = (powerHourId: PowerHourID) => {
  // Always define state hooks at the top level
  const [isReady, setIsReady] = useState(false);
  const [customError, setCustomError] = useState<Error | null>(null);

  // Server-side guard - return empty state if running on server
  if (typeof window === 'undefined') {
    return {
      powerHour: null,
      loading: true,
      error: null,
      connectionError: null,
      firestoreError: null
    };
  }

  // Always call useDocument with a valid reference, even if we're not ready yet
  // We'll create a ref that points to a non-existent document when not ready
  const docRef =
    isReady && isFirestoreAvailable() && powerHourId && firestore
      ? doc(firestore, COLLECTIONS.POWER_HOURS, powerHourId)
      : firestore
      ? doc(firestore, COLLECTIONS.POWER_HOURS, "placeholder-doc-id")
      : null;

  // Always call the hook, never conditionally
  const [snapshot, loading, firestoreError] = useDocument(docRef, {
    snapshotListenOptions: { includeMetadataChanges: false }
  });

  // Initialize Firebase on component mount
  useEffect(() => {
    // Clear any previous errors
    setCustomError(null);

    // Set custom error if no powerHourId is provided
    if (!powerHourId) {
      setCustomError(new Error("No power hour ID provided"));
      return;
    }

    // Initialize Firebase with error handling
    const initializeFirebase = () => {
      try {
        onFirebaseReady(() => {
          console.log("useFirestorePowerHour: Firebase ready");
          setIsReady(true);
          setCustomError(null); // Clear any previous connection errors
        });
      } catch (error) {
        console.error("useFirestorePowerHour: Firebase initialization failed:", error);
        setCustomError(error as Error);
        setIsReady(false);
      }
    };

    initializeFirebase();
  }, [powerHourId]);

  // Combine all error states
  const error = customError || firestoreError;

  // Determine actual loading state
  const isLoading =
    loading || !isReady || !isFirestoreAvailable() || !powerHourId;

  // Process the snapshot only when we're ready and have valid data
  let powerHour: PowerHour | null = null;
  if (!isLoading && snapshot?.exists() && !error) {
    const data = snapshot.data();
    powerHour = { id: snapshot.id, ...data } as PowerHour;

    // Enhanced logging for real-time property changes
    console.log(`🔄 Firestore update for ${powerHourId}:`, {
      currentStep: data.currentStep,
      stepProgress: data.stepProgress,
      title: data.title,
      songsCount: data.songs?.length || 0,
      videosCount: data.videos?.length || 0,
      entriesCount: data.entries?.length || 0,
      hasError: Boolean(data.error),
      lastUpdateTime: data.lastUpdateTime,
      timestamp: new Date().toISOString()
    });
  } else if (!isLoading && powerHourId && !snapshot?.exists() && !error) {
    // Document doesn't exist
    console.warn(`useFirestorePowerHour: Power hour with ID ${powerHourId} not found`);
  }

  // Log errors for debugging
  if (error) {
    console.error("useFirestorePowerHour: Error detected:", {
      customError: customError?.message,
      firestoreError: firestoreError?.message,
      combinedError: error.message
    });
  }

  return {
    powerHour,
    loading: isLoading,
    error,
    connectionError: customError,
    firestoreError
  };
};

/**
 * Hook to check if a power hour is complete
 * @param powerHourId The ID of the power hour to check
 * @returns Object containing completion status, loading state, and error
 */
export const useIsPowerHourComplete = (powerHourId: PowerHourID) => {
  const { powerHour, loading, error } = useFirestorePowerHour(powerHourId);

  // Check if the power hour is complete
  const isComplete =
    powerHour?.currentStep === PowerHourGenerationSteps.Complete;

  return { isComplete, loading, error };
};

/**
 * Hook to check if a power hour has any errors
 * @param powerHourId The ID of the power hour to check
 * @returns Object containing error status, loading state, and error
 */
export const useHasPowerHourErrors = (powerHourId: PowerHourID) => {
  const { powerHour, loading, error } = useFirestorePowerHour(powerHourId);

  // Check if the power hour has errors
  const hasErrors = Boolean(powerHour?.error);

  // The error can be either a string or an object with a message property
  let errorMessage: string | null = null;
  if (powerHour?.error) {
    if (typeof powerHour.error === "string") {
      errorMessage = powerHour.error;
    } else if (
      typeof powerHour.error === "object" &&
      powerHour.error !== null
    ) {
      // Try to get message from error object if it exists
      errorMessage = (powerHour.error as any).message || "Unknown error";
    }
  }

  return { hasErrors, errorMessage, loading, error };
};

/**
 * Hook to fetch multiple power hours from Firestore
 * @param limit Maximum number of power hours to fetch
 * @returns Object containing the power hours array, loading state, and error
 */
export const useFirestorePowerHours = (limitCount?: number) => {
  // Always define state hooks at the top level
  const [isReady, setIsReady] = useState(false);
  const [connectionError, setConnectionError] = useState<Error | null>(null);

  // Server-side guard - return empty state if running on server
  if (typeof window === 'undefined') {
    return {
      powerHours: [],
      loading: true,
      error: null,
      connectionError: null,
      firestoreError: null
    };
  }

  // Create a query that's valid even if we're not ready yet
  let powerHoursQuery;
  if (isReady && isFirestoreAvailable() && firestore) {
    try {
      // Valid query when ready - simplified without ordering since createdAt field may not exist
      if (typeof limitCount === "number") {
        powerHoursQuery = query(
          collection(firestore, COLLECTIONS.POWER_HOURS),
          limit(limitCount)
        );
      } else {
        powerHoursQuery = query(
          collection(firestore, COLLECTIONS.POWER_HOURS)
        );
      }
      console.log("useFirestorePowerHours: Query created successfully");
    } catch (error) {
      console.error("useFirestorePowerHours: Error creating query:", error);
      setConnectionError(error as Error);
      powerHoursQuery = null;
    }
  } else if (firestore) {
    try {
      // Fallback query for when we're not ready
      // This ensures the hook is always called with a valid reference
      powerHoursQuery = query(collection(firestore, COLLECTIONS.POWER_HOURS));
    } catch (error) {
      console.error("useFirestorePowerHours: Error creating fallback query:", error);
      setConnectionError(error as Error);
      powerHoursQuery = null;
    }
  } else {
    console.warn("useFirestorePowerHours: Firestore not available");
    setConnectionError(new Error("Firestore is not initialized or available"));
    powerHoursQuery = null;
  }

  // Initialize on component mount - always call useEffect
  useEffect(() => {
    console.log("useFirestorePowerHours: Initializing Firebase...");

    const initializeFirebase = () => {
      try {
        onFirebaseReady(() => {
          console.log("useFirestorePowerHours: Firebase ready, setting isReady to true");
          setIsReady(true);
          setConnectionError(null); // Clear any previous connection errors
        });
      } catch (error) {
        console.error("useFirestorePowerHours: Firebase initialization failed:", error);
        setConnectionError(error as Error);
        setIsReady(false);
      }
    };

    initializeFirebase();
  }, []);

  // Always call the hook, never conditionally
  // Note: react-firebase-hooks can handle null queries
  const [snapshot, loading, firestoreError] = useCollection(powerHoursQuery);

  // Determine actual loading state
  const isLoading = loading || !isReady || !isFirestoreAvailable();

  // Combine connection errors and Firestore errors
  const combinedError = connectionError || firestoreError;

  // Helper function to transform Firestore data to PowerHour format
  const transformFirestoreData = (doc: any): PowerHour => {
    const data = doc.data();
    const id = doc.id;

    // Transform the data to match our PowerHour interface
    const transformed: PowerHour = {
      id,
      title: data.title || `Power Hour ${id.slice(0, 8)}`, // Generate title if missing
      search: data.search || data.title || "",
      description: data.description || `A power hour with ${data.entries?.length || data.songs?.length || 0} songs`,
      videos: data.videos || [], // Keep existing videos array if present
      songs: data.songs || [], // Keep existing songs array
      entries: data.entries || [], // Keep existing entries array
      mostSimilarGenre: data.mostSimilarGenre || "Pop", // Default genre
      genres: data.genres || [data.mostSimilarGenre || "Pop"], // Default genres array
      upvotes: data.upvotes || 0,
      downvotes: data.downvotes || 0,
      feedback: data.feedback,
      currentStep: data.currentStep,
      stepProgress: data.stepProgress,
      lastUpdateTime: data.lastUpdateTime || data.updatedAt,
      error: data.error,
      createdAt: data.createdAt || Date.now(), // Default to current time if missing
      updatedAt: data.updatedAt || data.lastUpdateTime || Date.now()
    };

    // If videos array is empty but we have entries with video data, populate videos from entries
    if ((!transformed.videos || transformed.videos.length === 0) && transformed.entries && transformed.entries.length > 0) {
      transformed.videos = transformed.entries.map(entry => entry.backup_video || entry.video).filter(video => video);
    }

    return transformed;
  };

  // Only process snapshot data when we're actually ready and no errors
  let powerHours: PowerHour[] = [];

  if (!isLoading && snapshot && !combinedError) {
    console.log("✅ Processing", snapshot.docs.length, "power hour documents");

    // Transform each document to match our PowerHour interface
    powerHours = snapshot.docs.map(transformFirestoreData);

    console.log("✅ Successfully processed", powerHours.length, "power hours");
    if (powerHours.length > 0) {
      console.log("✅ Sample power hour:", {
        id: powerHours[0].id,
        title: powerHours[0].title,
        songsCount: powerHours[0].songs?.length || 0,
        entriesCount: powerHours[0].entries?.length || 0
      });
    }
  } else {
    console.log("❌ Not processing snapshot:", {
      isLoading,
      hasSnapshot: !!snapshot,
      hasError: !!combinedError,
      errorMessage: combinedError?.message
    });
  }

  // Log errors for debugging
  if (combinedError) {
    console.error("useFirestorePowerHours: Error detected:", {
      connectionError: connectionError?.message,
      firestoreError: firestoreError?.message,
      combinedError: combinedError.message
    });
  }

  return {
    powerHours,
    loading: isLoading,
    error: combinedError,
    connectionError,
    firestoreError
  };
};
