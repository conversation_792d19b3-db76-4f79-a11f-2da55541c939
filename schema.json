{"$schema": "http://json-schema.org/draft-07/schema#", "title": "PowerHoursArray", "description": "An array of Power Hour objects, each representing a unique Power Hour entry.", "type": "array", "items": {"type": "object", "title": "PowerHour", "description": "A single Power Hour resource with associated metadata and data.", "properties": {"_name": {"type": "string", "description": "Full Firestore path to the Power Hour document."}, "_id": {"type": "string", "description": "Unique identifier (UUID) for the Power Hour."}, "_createTime": {"type": "string", "format": "date-time", "description": "Timestamp indicating when this document was created in the database."}, "_updateTime": {"type": "string", "format": "date-time", "description": "Timestamp indicating the last time this document was updated in the database."}, "songs": {"type": ["array", "null"], "description": "A list of songs used for the Power Hour, generated based on 'search' to represent the theme of the Power Hour. (Songs are generated to match the search which represents the theme of the power hour.)", "items": {"type": "object", "properties": {"title": {"type": "string", "description": "Title of the song."}, "artist": {"type": "string", "description": "Artist of the song."}, "duration": {"type": "integer", "description": "Duration of the song in seconds."}}, "required": ["title", "artist"]}}, "videos": {"type": ["array", "null"], "description": "A list of YouTube videos that match the songs, each represented by a 1-minute snippet using start and end times. (Videos are 1 min snippets from each video using YouTube start/end times.)", "items": {"type": "object", "properties": {"videoId": {"type": "string", "description": "Unique YouTube video identifier."}, "startTime": {"type": "integer", "description": "Start time of the 1-minute snippet, in seconds."}, "endTime": {"type": "integer", "description": "End time of the 1-minute snippet, in seconds."}}, "required": ["videoId", "startTime", "endTime"]}}, "songCount": {"type": ["integer", "null"], "description": "Total count of songs included in this Power Hour."}, "VideoCount": {"type": ["integer", "null"], "description": "Total count of video snippets included in this Power Hour."}, "downVotes": {"type": ["integer", "null"], "description": "Number of downvotes that this Power Hour has received."}, "upVotes": {"type": ["integer", "null"], "description": "Number of upvotes that this Power Hour has received."}, "createdAt": {"type": ["string", "null"], "format": "date-time", "description": "The date and time when this Power Hour was originally created."}, "title": {"type": ["string", "null"], "description": "User-defined title for the Power Hour."}, "tags": {"type": ["array", "null"], "description": "A list of tags or keywords associated with the Power Hour.", "items": {"type": "string"}}, "entries": {"type": ["array", "null"], "description": "An array of additional entry objects or data relevant to the Power Hour (e.g., an ordered list of songs and snippets).", "items": {"type": "object", "properties": {"entryType": {"type": "string", "description": "Type of entry (e.g., 'song', 'video', etc.)."}, "data": {"type": "string", "description": "Optional data or reference for this entry."}}}}, "search": {"type": ["string", "null"], "description": "The theme or search query used to generate the songs and videos for the Power Hour."}}, "required": ["_id", "_name"]}}